<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationRatingModel
 *
 * Model for the appx_application_rating table
 *
 * Database Table Structure:
 * - id: Primary key (auto increment)
 * - application_id: Foreign key to appx_application_details table
 * - rate_item_id: Foreign key to rate_items table
 * - score_achieved: Score achieved by the application for this rating item
 * - score_max: Maximum possible score for this rating item
 * - justification: Text justification/reasoning for the score given
 * - created_by: User ID who created this rating
 * - created_at: Timestamp when rating was created
 * - updated_by: User ID who last updated this rating
 * - updated_at: Timestamp when rating was last updated
 * - deleted_by: User ID who soft-deleted this rating
 * - deleted_at: Timestamp when rating was soft-deleted
 */
class AppxApplicationRatingModel extends Model
{
    protected $table         = 'appx_application_rating';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id',    // Foreign key to appx_application_details
        'rate_item_id',      // Foreign key to rate_items
        'score_achieved',    // Score achieved by the application
        'score_max',         // Maximum score for this item
        'justification',     // Justification of the score gained
        'created_by',        // User who created the rating
        'updated_by',        // User who last updated the rating
        'deleted_by'         // User who soft-deleted the rating
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id'  => 'required|numeric',
        'rate_item_id'    => 'required|numeric',
        'score_achieved'  => 'required|numeric',
        'score_max'       => 'required|numeric',
        'justification'   => 'required|string'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'rate_item_id' => [
            'required' => 'Rate item ID is required',
            'numeric'  => 'Rate item ID must be a number'
        ],
        'score_achieved' => [
            'required' => 'Score achieved is required',
            'numeric'  => 'Score achieved must be a number'
        ],
        'score_max' => [
            'required' => 'Maximum score is required',
            'numeric'  => 'Maximum score must be a number'
        ],
        'justification' => [
            'required' => 'Justification is required',
            'string'   => 'Justification must be a valid text'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get ratings by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getRatingsByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)->findAll();
    }

    /**
     * Get rating by application and rate item
     *
     * @param int $applicationId
     * @param int $rateItemId
     * @return array|null
     */
    public function getRatingByApplicationAndItem($applicationId, $rateItemId)
    {
        return $this->where('application_id', $applicationId)
                    ->where('rate_item_id', $rateItemId)
                    ->first();
    }

    /**
     * Get ratings by rate item ID
     *
     * @param int $rateItemId
     * @return array
     */
    public function getRatingsByRateItemId($rateItemId)
    {
        return $this->where('rate_item_id', $rateItemId)->findAll();
    }

    /**
     * Get total score for application
     *
     * @param int $applicationId
     * @return array
     */
    public function getTotalScoreByApplication($applicationId)
    {
        return $this->select('SUM(score_achieved) as total_achieved, SUM(score_max) as total_max')
                    ->where('application_id', $applicationId)
                    ->first();
    }

    /**
     * Get average scores by rate item
     *
     * @param int $rateItemId
     * @return array
     */
    public function getAverageScoresByRateItem($rateItemId)
    {
        return $this->select('AVG(score_achieved) as avg_achieved, AVG(score_max) as avg_max, COUNT(*) as count')
                    ->where('rate_item_id', $rateItemId)
                    ->first();
    }

    /**
     * Update or create rating
     *
     * @param int $applicationId Application ID
     * @param int $rateItemId Rate item ID
     * @param int $scoreAchieved Score achieved by the application
     * @param int $scoreMax Maximum possible score for this item
     * @param string $justification Justification for the score
     * @param int $userId User ID performing the action
     * @return bool
     */
    public function updateOrCreateRating($applicationId, $rateItemId, $scoreAchieved, $scoreMax, $justification = '', $userId = null)
    {
        $existing = $this->getRatingByApplicationAndItem($applicationId, $rateItemId);

        $data = [
            'application_id'  => $applicationId,
            'rate_item_id'    => $rateItemId,
            'score_achieved'  => $scoreAchieved,
            'score_max'       => $scoreMax,
            'justification'   => $justification
        ];

        if ($existing) {
            if ($userId !== null) {
                $data['updated_by'] = $userId;
            }
            return $this->update($existing['id'], $data);
        } else {
            if ($userId !== null) {
                $data['created_by'] = $userId;
            }
            return $this->insert($data);
        }
    }

    /**
     * Delete ratings by application ID
     *
     * @param int $applicationId
     * @param int $deletedBy
     * @return bool
     */
    public function deleteRatingsByApplication($applicationId, $deletedBy = null)
    {
        if ($deletedBy !== null) {
            $this->where('application_id', $applicationId)
                 ->set('deleted_by', $deletedBy);
        }
        
        return $this->where('application_id', $applicationId)->delete(null, true);
    }

    /**
     * Get rating statistics
     *
     * @return array
     */
    public function getRatingStatistics()
    {
        $stats = [];

        // Total ratings
        $stats['total'] = $this->countAllResults(false);

        // Average scores
        $avgScores = $this->select('AVG(score_achieved) as avg_achieved, AVG(score_max) as avg_max')
                          ->first();

        $stats['average_scores'] = [
            'achieved' => round($avgScores['avg_achieved'], 2),
            'max'      => round($avgScores['avg_max'], 2)
        ];

        // Score distribution
        $scoreDistribution = $this->select('score_achieved, COUNT(*) as count')
                                  ->groupBy('score_achieved')
                                  ->orderBy('score_achieved', 'ASC')
                                  ->findAll();

        $stats['score_distribution'] = [];
        foreach ($scoreDistribution as $dist) {
            $stats['score_distribution'][$dist['score_achieved']] = $dist['count'];
        }

        return $stats;
    }

    /**
     * Get top rated applications
     *
     * @param int $limit Number of top applications to return
     * @return array
     */
    public function getTopRatedApplications($limit = 10)
    {
        return $this->select('application_id, SUM(score_achieved) as total_score')
                    ->groupBy('application_id')
                    ->orderBy('total_score', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }

    /**
     * Get ratings with justifications by application ID
     *
     * @param int $applicationId Application ID
     * @return array
     */
    public function getRatingsWithJustificationsByApplicationId($applicationId)
    {
        return $this->select('
                appx_application_rating.*,
                rate_items.item_label,
                rate_items.description as item_description
            ')
            ->join('rate_items', 'appx_application_rating.rate_item_id = rate_items.id', 'left')
            ->where('application_id', $applicationId)
            ->orderBy('rate_items.item_label', 'ASC')
            ->findAll();
    }

    /**
     * Get rating summary for an application
     *
     * @param int $applicationId Application ID
     * @return array
     */
    public function getRatingSummary($applicationId)
    {
        $ratings = $this->getRatingsWithJustificationsByApplicationId($applicationId);
        $totals = $this->getTotalScoreByApplication($applicationId);

        return [
            'ratings' => $ratings,
            'total_achieved' => $totals['total_achieved'] ?? 0,
            'total_max' => $totals['total_max'] ?? 0,
            'percentage' => $totals['total_max'] > 0 ?
                round(($totals['total_achieved'] / $totals['total_max']) * 100, 2) : 0
        ];
    }
}
