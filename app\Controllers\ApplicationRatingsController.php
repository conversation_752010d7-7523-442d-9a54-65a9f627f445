<?php

namespace App\Controllers;

use App\Models\AppxApplicationDetailsModel;
use App\Models\PositionsModel;
use App\Models\AppxApplicationRatingModel;
use App\Models\ExerciseModel;
use App\Models\AppxApplicationProfileModel;
use App\Models\RateItemsModel;
use App\Models\RateItemsScoresModel;
use App\Models\UsersModel;

class ApplicationRatingsController extends BaseController
{
    protected $applicationModel;
    protected $positionModel;
    protected $ratingModel;
    protected $exerciseModel;
    protected $profileModel;
    protected $rateItemsModel;
    protected $rateItemsScoresModel;
    protected $usersModel;

    public function __construct()
    {
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->positionModel = new PositionsModel();
        $this->ratingModel = new AppxApplicationRatingModel();
        $this->exerciseModel = new ExerciseModel();
        $this->profileModel = new AppxApplicationProfileModel();
        $this->rateItemsModel = new RateItemsModel();
        $this->rateItemsScoresModel = new RateItemsScoresModel();
        $this->usersModel = new UsersModel();
    }

    /**
     * List exercises for rating (Dashboard -> Exercises)
     */
    public function index()
    {
        // Get organization ID from session
        $orgId = session()->get('org_id');
        if (!$orgId) {
            session()->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercises with status = 'selection' for this organization (same as ProfiledApplicantsController)
        $exercises = $this->exerciseModel->where('status', 'selection')
                                        ->where('org_id', $orgId)
                                        ->orderBy('created_at', 'DESC')
                                        ->findAll();

        $data = [
            'title' => 'Rating - Select Exercise',
            'menu' => 'rating',
            'exercises' => $exercises
        ];

        return view('application_ratings/application_ratings_exercises', $data);
    }

    /**
     * List position groups for an exercise (Exercises -> Position Groups)
     */
    public function positionGroups($exerciseId)
    {
        // Load PositionsGroupModel for position groups
        $positionGroupModel = new \App\Models\PositionsGroupModel();

        // Get exercise details
        $exercise = $this->positionModel->select('
                exercises.id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('exercises.id', $exerciseId)
            ->first();

        if (!$exercise) {
            return redirect()->to('/rating')->with('error', 'Exercise not found');
        }

        // Get position groups for this exercise with position count
        $positionGroups = $positionGroupModel->getPositionGroupsWithCountByExerciseId($exerciseId);

        $data = [
            'title' => 'Rating - Select Position Group',
            'menu' => 'rating',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_ratings/application_ratings_position_groups', $data);
    }

    /**
     * List positions for a position group (Position Groups -> Positions)
     */
    public function positions($positionGroupId)
    {
        // Load PositionsGroupModel for position group details
        $positionGroupModel = new \App\Models\PositionsGroupModel();

        // Get position group details
        $positionGroup = $positionGroupModel->select('
                positions_groups.id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->where('positions_groups.id', $positionGroupId)
            ->first();

        if (!$positionGroup) {
            return redirect()->to('/rating')->with('error', 'Position group not found');
        }

        // Get positions in this group with application counts
        $positions = $this->positionModel->select('
                positions.id,
                positions.designation,
                positions.classification,
                positions.location,
                COUNT(appx_application_details.id) as application_count,
                SUM(CASE WHEN appx_application_details.rating_status = "completed" THEN 1 ELSE 0 END) as rated_count
            ')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('positions.position_group_id', $positionGroupId)
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Rating - Select Position',
            'menu' => 'rating',
            'positionGroup' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_ratings/application_ratings_positions', $data);
    }

    /**
     * List applications for a position (Positions -> Applications)
     */
    public function applications($positionId)
    {
        // Get position details with related information
        $position = $this->positionModel->select('
                positions.*,
                positions_groups.id as position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $positionId)
            ->first();

        if (!$position) {
            return redirect()->to('/rating')->with('error', 'Position not found');
        }

        // Get applications for this position that are ready for rating
        $applications = $this->applicationModel->select('
                appx_application_details.*,
                positions.designation as position_title
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->where('appx_application_details.position_id', $positionId)
            ->where('appx_application_details.profile_status', 'profiled')
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Rating - Applications',
            'menu' => 'rating',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_ratings/application_ratings_applications', $data);
    }

    /**
     * Show rating form for an application (GET)
     */
    public function rate($applicationId)
    {
        // Get application with position details
        $application = $this->applicationModel->getApplicationWithDetails($applicationId);

        if (!$application) {
            return redirect()->to('/rating')->with('error', 'Application not found');
        }

        // Get applicant profile for this exercise
        $profile = $this->profileModel->getProfiledApplicantDetails(
            $application['applicant_id'],
            $application['exercise_id']
        );

        // Get complete position details with JD file
        $position = $this->positionModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.id', $application['position_id'])
            ->first();

        // Get existing ratings for this application
        $existingRatings = $this->ratingModel->getRatingsByApplicationId($applicationId);

        // Get user name if application has been rated
        $ratedByName = null;
        if (!empty($application['rated_by'])) {
            $ratedByUser = $this->usersModel->find($application['rated_by']);
            $ratedByName = $ratedByUser ? $ratedByUser['name'] : 'Unknown User';
        }

        // Get all rating items with their scores
        $ratingItems = $this->rateItemsModel->getAllItems();
        $ratingItemsWithScores = [];

        foreach ($ratingItems as $item) {
            $scores = $this->rateItemsScoresModel->getScoresByItemId($item['id']);
            $ratingItemsWithScores[] = [
                'item' => $item,
                'scores' => $scores
            ];
        }

        $data = [
            'title' => 'Rate Application - ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'rating',
            'application' => $application,
            'profile' => $profile,
            'position' => $position,
            'existingRatings' => $existingRatings,
            'ratingItems' => $ratingItemsWithScores,
            'ratedByName' => $ratedByName
        ];

        return view('application_ratings/application_ratings_form', $data);
    }
    /**
     * Submit rating (POST)
     */
    public function submitRating()
    {
        $applicationId = $this->request->getPost('application_id');
        $ratings = $this->request->getPost('ratings');
        $remarks = $this->request->getPost('remarks');

        if (empty($applicationId)) {
            return redirect()->back()->with('error', 'Application ID is required');
        }

        // Get application details
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return redirect()->to('/rating')->with('error', 'Application not found');
        }

        // Calculate total score
        $totalScore = 0;
        if (!empty($ratings)) {
            foreach ($ratings as $rateItemId => $score) {
                $totalScore += (int)$score;
            }
        }

        // Update application rating status
        $updateData = [
            'rating_status' => 'completed',
            'rating_remarks' => $remarks,
            'rating_capability_max' => $totalScore,
            'rated_by' => session()->get('user_id'),
            'rated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->applicationModel->update($applicationId, $updateData)) {
            // Save individual ratings if provided
            if (!empty($ratings)) {
                foreach ($ratings as $rateItemId => $score) {
                    // Get the maximum score for this rate item
                    $rateItemScores = $this->rateItemsScoresModel->getScoresByItemId($rateItemId);
                    $maxScore = !empty($rateItemScores) ? max(array_column($rateItemScores, 'score')) : (int)$score;

                    // Create individual justification for this specific rating item
                    // This is different from rating_remarks which is overall remarks for the application
                    $justification = "Score: {$score}/{$maxScore} - Rating provided during application evaluation.";

                    $this->ratingModel->updateOrCreateRating(
                        $applicationId,
                        $rateItemId,
                        (int)$score,           // score_achieved
                        $maxScore,             // score_max
                        $justification,        // justification (specific to this rating item)
                        session()->get('user_id')
                    );
                }
            }

            return redirect()->to('/rating/rate/' . $applicationId . '#ratingFormCard')
                ->with('success', 'Rating submitted successfully. Total Score: ' . $totalScore);
        } else {
            return redirect()->back()
                ->with('error', 'Failed to save rating')
                ->withInput();
        }
    }

    /**
     * Analyze age with specific score configurations
     */
    private function analyzeAgeWithScores($age, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Age scoring criteria not configured for this position', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $suggestedScore = 0;
        $reasoning = '';
        $match = 'Poor';
        $maxScore = max(array_column($scores, 'score'));

        // Build factual reasoning about age
        $ageRanges = [];
        foreach ($scores as $score) {
            $ageRanges[] = $score['label'] . ' (Score: ' . $score['score'] . ')';
        }

        $factualReasoning = "Applicant age is {$age} years. ";
        $factualReasoning .= "Available age scoring criteria: " . implode(', ', $ageRanges) . ". ";

        foreach ($scores as $score) {
            if ($this->ageInRange($age, $score['label']) || $this->ageInRange($age, $score['score_description'] ?? '')) {
                $suggestedScore = $score['score'];
                $factualReasoning .= "Applicant falls within the '{$score['label']}' category.";
                $match = $this->getMatchLevel($suggestedScore, $maxScore);
                break;
            }
        }

        if ($suggestedScore == 0) {
            // Try to find closest match
            $closestScore = $this->findClosestAgeScore($age, $scores);
            if ($closestScore) {
                $suggestedScore = $closestScore['score'];
                $factualReasoning .= "Closest matching category is '{$closestScore['label']}' with score {$closestScore['score']}.";
                $match = $this->getMatchLevel($suggestedScore, $maxScore);
            } else {
                $factualReasoning .= "No specific age range criteria matches the applicant's age.";
            }
        }

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $factualReasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze education with specific score configurations
     */
    private function analyzeEducationWithScores($applicantEducation, $positionRequirements, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Education scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $applicantEducation = strtolower($applicantEducation);
        $maxScore = max(array_column($scores, 'score'));
        $suggestedScore = 0;
        $reasoning = '';
        $match = 'Poor';

        // Look for education level matches in scores
        foreach ($scores as $score) {
            $scoreLabel = strtolower($score['label']);
            $scoreDesc = strtolower($score['score_description'] ?? '');

            if (strpos($applicantEducation, 'doctorate') !== false || strpos($applicantEducation, 'phd') !== false) {
                if (strpos($scoreLabel, 'doctorate') !== false || strpos($scoreDesc, 'doctorate') !== false) {
                    $suggestedScore = $score['score'];
                    $reasoning = "Doctorate/PhD qualification - {$score['label']}";
                    break;
                }
            } elseif (strpos($applicantEducation, 'master') !== false) {
                if (strpos($scoreLabel, 'master') !== false || strpos($scoreDesc, 'master') !== false) {
                    $suggestedScore = $score['score'];
                    $reasoning = "Master's degree qualification - {$score['label']}";
                    break;
                }
            } elseif (strpos($applicantEducation, 'bachelor') !== false || strpos($applicantEducation, 'degree') !== false) {
                if (strpos($scoreLabel, 'bachelor') !== false || strpos($scoreDesc, 'bachelor') !== false) {
                    $suggestedScore = $score['score'];
                    $reasoning = "Bachelor's degree qualification - {$score['label']}";
                    break;
                }
            } elseif (strpos($applicantEducation, 'diploma') !== false) {
                if (strpos($scoreLabel, 'diploma') !== false || strpos($scoreDesc, 'diploma') !== false) {
                    $suggestedScore = $score['score'];
                    $reasoning = "Diploma qualification - {$score['label']}";
                    break;
                }
            } elseif (strpos($applicantEducation, 'certificate') !== false) {
                if (strpos($scoreLabel, 'certificate') !== false || strpos($scoreDesc, 'certificate') !== false) {
                    $suggestedScore = $score['score'];
                    $reasoning = "Certificate qualification - {$score['label']}";
                    break;
                }
            }
        }

        if ($suggestedScore == 0) {
            // Default to lowest score if no match found
            $lowestScore = min(array_column($scores, 'score'));
            $suggestedScore = $lowestScore;
            $reasoning = "Education level unclear - Minimum score assigned";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze experience with specific score configurations
     */
    private function analyzeExperienceWithScores($applicantExperience, $positionRequirements, $scores, $item, $itemLabel)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Experience scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));
        $suggestedScore = 0;
        $reasoning = '';

        // Extract years of experience from applicant profile
        $applicantYears = $this->extractYearsOfExperience($applicantExperience);

        // Find matching score based on years of experience
        foreach ($scores as $score) {
            $scoreLabel = strtolower($score['label']);
            $scoreDesc = strtolower($score['score_description'] ?? '');

            if ($this->experienceInRange($applicantYears, $scoreLabel) ||
                $this->experienceInRange($applicantYears, $scoreDesc)) {
                $suggestedScore = $score['score'];
                $reasoning = "{$applicantYears} years experience - {$score['label']}";
                break;
            }
        }

        if ($suggestedScore == 0) {
            // Find closest match
            $closestScore = $this->findClosestExperienceScore($applicantYears, $scores);
            if ($closestScore) {
                $suggestedScore = $closestScore['score'];
                $reasoning = "{$applicantYears} years experience - Closest: {$closestScore['label']}";
            } else {
                $suggestedScore = min(array_column($scores, 'score'));
                $reasoning = "{$applicantYears} years experience - No exact match";
            }
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze skills with specific score configurations
     */
    private function analyzeSkillsWithScores($applicantSkills, $positionSkills, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Skills scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));
        $applicantSkills = strtolower($applicantSkills);
        $positionSkills = strtolower($positionSkills);

        // Look for skill-related keywords
        $skillKeywords = ['skill', 'competenc', 'abilit', 'proficien', 'expert', 'experience'];
        $skillCount = 0;

        foreach ($skillKeywords as $keyword) {
            if (strpos($applicantSkills, $keyword) !== false) {
                $skillCount++;
            }
        }

        // Adjust score based on skill indicators
        if ($skillCount >= 4) {
            $suggestedScore = $maxScore;
            $reasoning = "Strong skills profile - Multiple competency indicators found";
        } elseif ($skillCount >= 2) {
            $suggestedScore = intval($maxScore * 0.8);
            $reasoning = "Good skills profile - Some competency indicators found";
        } elseif ($skillCount >= 1) {
            $suggestedScore = intval($maxScore * 0.6);
            $reasoning = "Basic skills profile - Limited competency indicators";
        } else {
            $suggestedScore = intval($maxScore * 0.3);
            $reasoning = "Skills information limited - Requires verification";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze knowledge with specific score configurations
     */
    private function analyzeKnowledgeWithScores($applicantKnowledge, $positionKnowledge, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Knowledge scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));
        $applicantKnowledge = strtolower($applicantKnowledge);

        // Look for knowledge-related keywords
        $knowledgeKeywords = ['knowledge', 'familiar', 'understand', 'aware', 'know', 'experience'];
        $knowledgeCount = 0;

        foreach ($knowledgeKeywords as $keyword) {
            if (strpos($applicantKnowledge, $keyword) !== false) {
                $knowledgeCount++;
            }
        }

        // Adjust score based on knowledge indicators
        if ($knowledgeCount >= 4) {
            $suggestedScore = $maxScore;
            $reasoning = "Strong knowledge base - Multiple indicators found";
        } elseif ($knowledgeCount >= 2) {
            $suggestedScore = intval($maxScore * 0.8);
            $reasoning = "Good knowledge base - Some indicators found";
        } elseif ($knowledgeCount >= 1) {
            $suggestedScore = intval($maxScore * 0.6);
            $reasoning = "Basic knowledge base - Limited indicators";
        } else {
            $suggestedScore = intval($maxScore * 0.3);
            $reasoning = "Knowledge information limited - Requires verification";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze training with specific score configurations
     */
    private function analyzeTrainingWithScores($applicantTraining, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Training scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));
        $applicantTraining = strtolower($applicantTraining);

        // Count training indicators
        $trainingKeywords = ['training', 'course', 'workshop', 'seminar', 'certification', 'program'];
        $trainingCount = 0;

        foreach ($trainingKeywords as $keyword) {
            $trainingCount += substr_count($applicantTraining, $keyword);
        }

        // Score based on training quantity and quality
        if ($trainingCount >= 5) {
            $suggestedScore = $maxScore;
            $reasoning = "Extensive training record - {$trainingCount} training indicators";
        } elseif ($trainingCount >= 3) {
            $suggestedScore = intval($maxScore * 0.8);
            $reasoning = "Good training record - {$trainingCount} training indicators";
        } elseif ($trainingCount >= 1) {
            $suggestedScore = intval($maxScore * 0.6);
            $reasoning = "Some training record - {$trainingCount} training indicators";
        } else {
            $suggestedScore = intval($maxScore * 0.2);
            $reasoning = "Training information limited - Requires verification";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze capability with specific score configurations
     */
    private function analyzeCapabilityWithScores($profile, $position, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Capability scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));

        // Assess overall capability based on multiple factors
        $capabilityFactors = [
            'education' => !empty($profile['qualification_text']),
            'experience' => !empty($profile['job_experiences']),
            'skills' => !empty($profile['skills_competencies']),
            'knowledge' => !empty($profile['knowledge']),
            'training' => !empty($profile['trainings'])
        ];

        $capabilityScore = array_sum($capabilityFactors);

        if ($capabilityScore >= 4) {
            $suggestedScore = $maxScore;
            $reasoning = "High capability - Strong across multiple areas";
        } elseif ($capabilityScore >= 3) {
            $suggestedScore = intval($maxScore * 0.8);
            $reasoning = "Good capability - Demonstrated in most areas";
        } elseif ($capabilityScore >= 2) {
            $suggestedScore = intval($maxScore * 0.6);
            $reasoning = "Average capability - Some areas demonstrated";
        } else {
            $suggestedScore = intval($maxScore * 0.3);
            $reasoning = "Capability information limited - Requires verification";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Analyze public service status with specific score configurations
     */
    private function analyzePublicServiceStatusWithScores($currentEmployer, $scores, $item)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Public service status scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));
        $currentEmployer = strtolower($currentEmployer);
        $suggestedScore = 0;
        $reasoning = '';

        // Check for public service indicators
        $publicServiceKeywords = ['government', 'department', 'ministry', 'public', 'state', 'provincial', 'national'];
        $isPublicServant = false;

        foreach ($publicServiceKeywords as $keyword) {
            if (strpos($currentEmployer, $keyword) !== false) {
                $isPublicServant = true;
                break;
            }
        }

        // Find appropriate score based on employment status
        foreach ($scores as $score) {
            $scoreLabel = strtolower($score['label']);
            $scoreDesc = strtolower($score['score_description'] ?? '');

            if ($isPublicServant && (strpos($scoreLabel, 'public servant') !== false || strpos($scoreDesc, 'public servant') !== false)) {
                $suggestedScore = $score['score'];
                $reasoning = "Current public service employment - {$score['label']}";
                break;
            } elseif (!$isPublicServant && (strpos($scoreLabel, 'non-public') !== false || strpos($scoreDesc, 'not employed') !== false)) {
                $suggestedScore = $score['score'];
                $reasoning = "Non-public service employment - {$score['label']}";
                break;
            } elseif (strpos($scoreLabel, 'unemployed') !== false && empty($currentEmployer)) {
                $suggestedScore = $score['score'];
                $reasoning = "Currently unemployed - {$score['label']}";
                break;
            }
        }

        if ($suggestedScore == 0) {
            $suggestedScore = min(array_column($scores, 'score'));
            $reasoning = "Employment status unclear - Minimum score assigned";
        }

        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Generic analysis for criteria that don't have specific handlers
     */
    private function analyzeGenericCriteria($item, $scores, $application, $profile, $position)
    {
        if (empty($scores)) {
            return ['score' => 0, 'maxScore' => 0, 'reasoning' => 'Scoring not configured', 'match' => 'Unknown', 'itemId' => $item['id']];
        }

        $maxScore = max(array_column($scores, 'score'));

        // Default to middle score for generic criteria
        $suggestedScore = intval($maxScore / 2);
        $reasoning = "{$item['item_label']} - Requires manual assessment";
        $match = $this->getMatchLevel($suggestedScore, $maxScore);

        return [
            'score' => $suggestedScore,
            'maxScore' => $maxScore,
            'reasoning' => $reasoning,
            'match' => $match,
            'itemId' => $item['id']
        ];
    }

    /**
     * Helper function to determine match level
     */
    private function getMatchLevel($score, $maxScore)
    {
        if ($maxScore == 0) return 'Unknown';

        $percentage = ($score / $maxScore) * 100;

        if ($percentage >= 90) return 'Excellent';
        if ($percentage >= 80) return 'Very Good';
        if ($percentage >= 70) return 'Good';
        if ($percentage >= 60) return 'Fair';
        if ($percentage >= 40) return 'Poor';
        return 'Very Poor';
    }



    /**
     * Find closest age score match
     */
    private function findClosestAgeScore($age, $scores)
    {
        $closestScore = null;
        $closestDiff = PHP_INT_MAX;

        foreach ($scores as $score) {
            // Try to extract age from label or description
            $ageFromLabel = $this->extractAgeFromText($score['label']);
            $ageFromDesc = $this->extractAgeFromText($score['score_description'] ?? '');

            $targetAge = $ageFromLabel ?: $ageFromDesc;

            if ($targetAge) {
                $diff = abs($age - $targetAge);
                if ($diff < $closestDiff) {
                    $closestDiff = $diff;
                    $closestScore = $score;
                }
            }
        }

        return $closestScore;
    }

    /**
     * Extract age from text
     */
    private function extractAgeFromText($text)
    {
        if (preg_match('/(\d+)-(\d+)/', $text, $matches)) {
            return intval(($matches[1] + $matches[2]) / 2); // Return middle of range
        }

        if (preg_match('/(\d+)\+/', $text, $matches)) {
            return intval($matches[1]) + 5; // Assume +5 years for open ranges
        }

        if (preg_match('/(\d+)/', $text, $matches)) {
            return intval($matches[1]);
        }

        return null;
    }





    /**
     * Find closest experience score match
     */
    private function findClosestExperienceScore($years, $scores)
    {
        $closestScore = null;
        $closestDiff = PHP_INT_MAX;

        foreach ($scores as $score) {
            $yearsFromLabel = $this->extractYearsFromText($score['label']);
            $yearsFromDesc = $this->extractYearsFromText($score['score_description'] ?? '');

            $targetYears = $yearsFromLabel ?: $yearsFromDesc;

            if ($targetYears !== null) {
                $diff = abs($years - $targetYears);
                if ($diff < $closestDiff) {
                    $closestDiff = $diff;
                    $closestScore = $score;
                }
            }
        }

        return $closestScore;
    }

    /**
     * Extract years from text
     */
    private function extractYearsFromText($text)
    {
        if (preg_match('/(\d+)-(\d+)\s*years?/i', $text, $matches)) {
            return intval(($matches[1] + $matches[2]) / 2); // Return middle of range
        }

        if (preg_match('/(\d+)\+\s*years?/i', $text, $matches)) {
            return intval($matches[1]) + 2; // Assume +2 years for open ranges
        }

        if (preg_match('/(\d+)\s*years?/i', $text, $matches)) {
            return intval($matches[1]);
        }

        return null;
    }
}
